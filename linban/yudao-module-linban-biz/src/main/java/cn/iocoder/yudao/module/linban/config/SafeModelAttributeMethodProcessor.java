package cn.iocoder.yudao.module.linban.config;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.mvc.method.annotation.ServletModelAttributeMethodProcessor;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 安全的模型属性方法处理器
 * 用于预处理请求参数，将"undefined"等无效值转换为null
 * 
 * <AUTHOR>
 */
public class SafeModelAttributeMethodProcessor extends ServletModelAttributeMethodProcessor {

    public SafeModelAttributeMethodProcessor(boolean annotationNotRequired) {
        super(annotationNotRequired);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        
        // 预处理请求参数
        preprocessRequestParameters(webRequest);
        
        return super.resolveArgument(parameter, mavContainer, webRequest, binderFactory);
    }

    /**
     * 预处理请求参数，将"undefined"等无效值替换为空字符串
     */
    private void preprocessRequestParameters(NativeWebRequest webRequest) {
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
        if (request != null) {
            Map<String, String[]> parameterMap = request.getParameterMap();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String[] values = entry.getValue();
                if (values != null) {
                    for (int i = 0; i < values.length; i++) {
                        if ("undefined".equalsIgnoreCase(values[i]) || 
                            "null".equalsIgnoreCase(values[i])) {
                            values[i] = "";
                        }
                    }
                }
            }
        }
    }
}
