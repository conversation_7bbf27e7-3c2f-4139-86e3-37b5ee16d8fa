package cn.iocoder.yudao.module.linban.config;

import cn.hutool.core.util.StrUtil;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * 安全的String到Long转换器
 * 用于处理前端传递的"undefined"、"null"等无效值
 * 
 * <AUTHOR>
 */
@Component
public class SafeStringToLongConverter implements Converter<String, Long> {

    @Override
    public Long convert(String source) {
        // 处理空值或无效值
        if (StrUtil.isBlank(source) || 
            "undefined".equalsIgnoreCase(source) || 
            "null".equalsIgnoreCase(source)) {
            return null;
        }
        
        try {
            return Long.parseLong(source.trim());
        } catch (NumberFormatException e) {
            // 如果无法解析，返回null而不是抛出异常
            return null;
        }
    }
}
