package cn.iocoder.yudao.module.linban.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置
 * 
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private SafeStringToLongConverter safeStringToLongConverter;

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(safeStringToLongConverter);
    }
}
