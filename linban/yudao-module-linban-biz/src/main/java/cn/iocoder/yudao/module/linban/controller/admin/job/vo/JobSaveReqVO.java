package cn.iocoder.yudao.module.linban.controller.admin.job.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;


@Schema(description = "管理后台 - 林班-工作新增/修改 Request VO")
@Data
public class JobSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6693")
    private Long id;

    @Schema(description = "工作名称", example = "王五")
    private String jobName;

}