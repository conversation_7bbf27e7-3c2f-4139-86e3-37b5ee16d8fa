package cn.iocoder.yudao.module.linban.controller.admin.label;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.linban.controller.admin.label.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.label.LabelDO;
import cn.iocoder.yudao.module.linban.service.label.LabelService;

@Tag(name = "管理后台 - 林班-标记点")
@RestController
@RequestMapping("/linban/label")
@Validated
public class LabelController {

    @Resource
    private LabelService labelService;

    @PostMapping("/create")
    @Operation(summary = "创建林班-标记点")
    @PreAuthorize("@ss.hasPermission('linban:label:create')")
    public CommonResult<Long> createLabel(@Valid @RequestBody LabelSaveReqVO createReqVO) {
        return success(labelService.createLabel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新林班-标记点")
    @PreAuthorize("@ss.hasPermission('linban:label:update')")
    public CommonResult<Boolean> updateLabel(@Valid @RequestBody LabelSaveReqVO updateReqVO) {
        labelService.updateLabel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除林班-标记点")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('linban:label:delete')")
    public CommonResult<Boolean> deleteLabel(@RequestParam("id") Long id) {
        labelService.deleteLabel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得林班-标记点")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('linban:label:query')")
    public CommonResult<LabelRespVO> getLabel(@RequestParam("id") Long id) {
        LabelDO label = labelService.getLabel(id);
        return success(BeanUtils.toBean(label, LabelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得林班-标记点分页")
    @PreAuthorize("@ss.hasPermission('linban:label:query')")
    public CommonResult<PageResult<LabelRespVO>> getLabelPage(@Valid LabelPageReqVO pageReqVO) {
        PageResult<LabelDO> pageResult = labelService.getLabelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LabelRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "列表")
    public CommonResult<List<LabelDO>> listByPageReq(LabelPageReqVO pageReqVO){
        return CommonResult.success(labelService.listByPageReq(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出林班-标记点 Excel")
    @PreAuthorize("@ss.hasPermission('linban:label:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLabelExcel(@Valid LabelPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LabelDO> list = labelService.getLabelPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "林班-标记点.xls", "数据", LabelRespVO.class,
                        BeanUtils.toBean(list, LabelRespVO.class));
    }

}