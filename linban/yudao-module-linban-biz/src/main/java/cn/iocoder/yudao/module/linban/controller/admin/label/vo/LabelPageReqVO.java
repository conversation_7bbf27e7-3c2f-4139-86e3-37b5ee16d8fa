package cn.iocoder.yudao.module.linban.controller.admin.label.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import cn.iocoder.yudao.module.linban.utils.SafeLongDeserializer;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 林班-标记点分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LabelPageReqVO extends PageParam {

    @Schema(description = "标记点名称", example = "王五")
    private String labelName;

    @Schema(description = "标记点备注", example = "随便")
    private String labelRemark;

    @Schema(description = "用户编号", example = "390")
    @JsonDeserialize(using = SafeLongDeserializer.class)
    private Long userId;

    @Schema(description = "林班编号", example = "390")
    @JsonDeserialize(using = SafeLongDeserializer.class)
    private Long quartelId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "收藏夹id")
    @JsonDeserialize(using = SafeLongDeserializer.class)
    private Long folderId;

    private Set<Long> ids;

}