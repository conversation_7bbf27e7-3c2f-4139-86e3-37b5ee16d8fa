package cn.iocoder.yudao.module.linban.controller.admin.labelfolder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.linban.controller.admin.labelfolder.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.labelfolder.LabelFolderDO;
import cn.iocoder.yudao.module.linban.service.labelfolder.LabelFolderService;

@Tag(name = "管理后台 - 林班-标记点-收藏夹关联")
@RestController
@RequestMapping("/linban/label-folder")
@Validated
public class LabelFolderController {

    @Resource
    private LabelFolderService labelFolderService;

    @PostMapping("/create")
    @Operation(summary = "创建林班-标记点-收藏夹关联")
    @PreAuthorize("@ss.hasPermission('linban:label-folder:create')")
    public CommonResult<Long> createLabelFolder(@Valid @RequestBody LabelFolderSaveReqVO createReqVO) {
        return success(labelFolderService.createLabelFolder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新林班-标记点-收藏夹关联")
    @PreAuthorize("@ss.hasPermission('linban:label-folder:update')")
    public CommonResult<Boolean> updateLabelFolder(@Valid @RequestBody LabelFolderSaveReqVO updateReqVO) {
        labelFolderService.updateLabelFolder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除林班-标记点-收藏夹关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('linban:label-folder:delete')")
    public CommonResult<Boolean> deleteLabelFolder(@RequestParam("id") Long id) {
        labelFolderService.deleteLabelFolder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得林班-标记点-收藏夹关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('linban:label-folder:query')")
    public CommonResult<LabelFolderRespVO> getLabelFolder(@RequestParam("id") Long id) {
        LabelFolderDO labelFolder = labelFolderService.getLabelFolder(id);
        return success(BeanUtils.toBean(labelFolder, LabelFolderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得林班-标记点-收藏夹关联分页")
    @PreAuthorize("@ss.hasPermission('linban:label-folder:query')")
    public CommonResult<PageResult<LabelFolderRespVO>> getLabelFolderPage(@Valid LabelFolderPageReqVO pageReqVO) {
        PageResult<LabelFolderDO> pageResult = labelFolderService.getLabelFolderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LabelFolderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出林班-标记点-收藏夹关联 Excel")
    @PreAuthorize("@ss.hasPermission('linban:label-folder:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLabelFolderExcel(@Valid LabelFolderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LabelFolderDO> list = labelFolderService.getLabelFolderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "林班-标记点-收藏夹关联.xls", "数据", LabelFolderRespVO.class,
                        BeanUtils.toBean(list, LabelFolderRespVO.class));
    }

}