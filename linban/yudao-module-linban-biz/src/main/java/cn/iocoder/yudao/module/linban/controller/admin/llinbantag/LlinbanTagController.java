package cn.iocoder.yudao.module.linban.controller.admin.llinbantag;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.linban.controller.admin.llinbantag.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.llinbantag.LlinbanTagDO;
import cn.iocoder.yudao.module.linban.service.llinbantag.LlinbanTagService;

@Tag(name = "管理后台 - 林班-标签")
@RestController
@RequestMapping("/linban/llinban-tag")
@Validated
public class LlinbanTagController {

    @Resource
    private LlinbanTagService llinbanTagService;

    @PostMapping("/create")
    @Operation(summary = "创建林班-标签")
    @PreAuthorize("@ss.hasPermission('linban:llinban-tag:create')")
    public CommonResult<Long> createLlinbanTag(@Valid @RequestBody LlinbanTagSaveReqVO createReqVO) {
        return success(llinbanTagService.createLlinbanTag(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新林班-标签")
    @PreAuthorize("@ss.hasPermission('linban:llinban-tag:update')")
    public CommonResult<Boolean> updateLlinbanTag(@Valid @RequestBody LlinbanTagSaveReqVO updateReqVO) {
        llinbanTagService.updateLlinbanTag(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除林班-标签")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('linban:llinban-tag:delete')")
    public CommonResult<Boolean> deleteLlinbanTag(@RequestParam("id") Long id) {
        llinbanTagService.deleteLlinbanTag(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得林班-标签")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('linban:llinban-tag:query')")
    public CommonResult<LlinbanTagRespVO> getLlinbanTag(@RequestParam("id") Long id) {
        LlinbanTagDO llinbanTag = llinbanTagService.getLlinbanTag(id);
        return success(BeanUtils.toBean(llinbanTag, LlinbanTagRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得林班-标签分页")
    @PreAuthorize("@ss.hasPermission('linban:llinban-tag:query')")
    public CommonResult<PageResult<LlinbanTagRespVO>> getLlinbanTagPage(@Valid LlinbanTagPageReqVO pageReqVO) {
        PageResult<LlinbanTagDO> pageResult = llinbanTagService.getLlinbanTagPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LlinbanTagRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出林班-标签 Excel")
    @PreAuthorize("@ss.hasPermission('linban:llinban-tag:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLlinbanTagExcel(@Valid LlinbanTagPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LlinbanTagDO> list = llinbanTagService.getLlinbanTagPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "林班-标签.xls", "数据", LlinbanTagRespVO.class,
                        BeanUtils.toBean(list, LlinbanTagRespVO.class));
    }

}