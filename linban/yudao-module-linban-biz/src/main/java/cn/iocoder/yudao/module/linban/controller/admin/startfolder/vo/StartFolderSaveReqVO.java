package cn.iocoder.yudao.module.linban.controller.admin.startfolder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import static  cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

@Schema(description = "管理后台 - 林班-收藏夹新增/修改 Request VO")
@Data
public class StartFolderSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19416")
    private Long id;

    @Schema(description = "用户编号", example = "31420")
    private Long userId;

    @Schema(description = "类型", example = "1")
    private Integer type;

    @Schema(description = "类型名称个人企业", example = "赵六")
    private String typeName;

    @Schema(description = "收藏夹名称", example = "李四")
    private String folderName;

    @Schema(description = "收藏夹计数", example = "10186")
    private Integer count;

}