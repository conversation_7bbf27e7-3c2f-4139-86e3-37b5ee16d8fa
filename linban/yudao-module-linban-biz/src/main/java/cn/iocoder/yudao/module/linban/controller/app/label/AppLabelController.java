package cn.iocoder.yudao.module.linban.controller.app.label;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.linban.controller.admin.label.vo.LabelPageReqVO;
import cn.iocoder.yudao.module.linban.controller.admin.label.vo.LabelRespVO;
import cn.iocoder.yudao.module.linban.controller.admin.label.vo.LabelSaveReqVO;
import cn.iocoder.yudao.module.linban.dal.dataobject.label.LabelDO;
import cn.iocoder.yudao.module.linban.service.label.LabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Slf4j
@Tag(name = "用户后台 - 林班-标记点")
@RestController
@RequestMapping("/linban/label")
@Validated
public class AppLabelController {

    @Resource
    private LabelService labelService;

    @PostMapping("/create")
    @Operation(summary = "创建林班-标记点")
    @PermitAll
    public CommonResult<Long> createLabel(@Valid @RequestBody LabelSaveReqVO createReqVO) {

        log.info("req = \n {}",createReqVO);

        return success(labelService.createLabel(createReqVO));
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "更新林班-标记点")
    @PermitAll
    public CommonResult<Boolean> updateLabel(@Valid @RequestBody LabelSaveReqVO updateReqVO,@PathVariable("id") Long id) {
        if(id != null){
            updateReqVO.setId(id);
        }
        labelService.updateLabel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除林班-标记点")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteLabel(@RequestParam("id") Long id) {
        labelService.deleteLabel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得林班-标记点")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<LabelDO> getLabel(@RequestParam("id") Long id) {
        LabelDO label = labelService.getLabel(id);
        return success(label);
    }

    @GetMapping("/page")
    @Operation(summary = "获得林班-标记点分页")
    @PermitAll
    public CommonResult<PageResult<LabelDO>> getLabelPage(@Valid LabelPageReqVO pageReqVO) {
        PageResult<LabelDO> pageResult = labelService.getLabelPage(pageReqVO);
        return success(pageResult);
    }

    @PermitAll
    @GetMapping("/list")
    @Operation(summary = "列表")
    public CommonResult<List<LabelDO>> listByPageReq(LabelPageReqVO pageReqVO){
        return CommonResult.success(labelService.listByPageReq(pageReqVO));
    }


}