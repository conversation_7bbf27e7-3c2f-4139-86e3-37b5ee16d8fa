package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户App - 本地分片上传 Response VO")
@Data
public class LocalChunkUploadRespVO {

    @Schema(description = "上传任务ID")
    private String uploadId;

    @Schema(description = "分片序号")
    private Integer chunkIndex;

    @Schema(description = "上传是否成功")
    private Boolean success;

    @Schema(description = "当前上传进度")
    private Double progress;

    @Schema(description = "已上传分片数")
    private Integer uploadedChunks;

    @Schema(description = "总分片数")
    private Integer totalChunks;

    @Schema(description = "分片保存路径")
    private String chunkPath;

    @Schema(description = "错误信息")
    private String errorMessage;
} 