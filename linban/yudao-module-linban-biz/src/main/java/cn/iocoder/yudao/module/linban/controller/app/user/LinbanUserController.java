package cn.iocoder.yudao.module.linban.controller.app.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.req.UpdateUserAliveReq;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.res.AliveByJobRes;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.res.LinbanRes;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.res.LoginRes;
import cn.iocoder.yudao.module.linban.controller.app.vo.linban.LinbanUserRelation;
import cn.iocoder.yudao.module.linban.controller.app.vo.user.LoginAccountReq;
import cn.iocoder.yudao.module.linban.controller.app.vo.user.UserVo;
import cn.iocoder.yudao.module.linban.dal.dataobject.label.LabelDO;
import cn.iocoder.yudao.module.linban.dal.dataobject.linban.LinbanDO;
import cn.iocoder.yudao.module.linban.dal.dataobject.user.LinbanUser;
import cn.iocoder.yudao.module.linban.dal.mongo.friend.Friend;
import cn.iocoder.yudao.module.linban.service.label.LabelService;
import cn.iocoder.yudao.module.linban.service.user.LinBanUserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "用户app - 同步")
@RestController
@RequestMapping("/linban/user")
public class LinbanUserController {

    @Resource
    private LinBanUserService linBanUserService;

    @PermitAll()
    @GetMapping("/list")
    @Operation(summary = "从用户信息获取用户列表")
    public CommonResult<List<LinbanUser>> list(@ModelAttribute LinbanUser req) {
        List<LinbanUser> res = linBanUserService.listByUser(req);
        return CommonResult.success(res);
    }

    @PermitAll
    @GetMapping("/list-by-Ids")
    @Operation(summary = "用户列表")
    public CommonResult<List<LinbanUser>> listByIds(@ModelAttribute LinbanUser req) {
        List<LinbanUser> res = linBanUserService.listByIds(req.getUserIds());
        return CommonResult.success(res);
    }

    @PermitAll()
    @GetMapping("/alive-by-job")
    @Operation(summary = "从工作分类获取在线人数")
    public CommonResult<AliveByJobRes> aliveByJob() {
        AliveByJobRes res = linBanUserService.aliveByJob();
        return CommonResult.success(res);
    }

    @PermitAll()
    @GetMapping("/alive-by-job-group")
    @Operation(summary = "从工作分类获取在线人数")
    public CommonResult<AliveByJobRes> aliveByJobGroup() {
        AliveByJobRes res = linBanUserService.aliveByJobGroup();
        return CommonResult.success(res);
    }


    @PermitAll
    @PutMapping("/update-user-alive")
    @Operation(summary = "更新用户轨迹状态")
    public CommonResult<LinbanUser> updateUserAlive(@RequestBody UpdateUserAliveReq req){
        LinbanUser res = linBanUserService.updateUserAlive(req);
        return CommonResult.success(res);
    }


    @PermitAll()
    @GetMapping("/get")
    @Operation(summary = "获取用户详情")
    public CommonResult<LinbanUser> getUser(@RequestParam("id") Long id) {
        LinbanUser res = linBanUserService.getById(id);
        return CommonResult.success(res);
    }


    @PermitAll()
    @GetMapping("/sync-all-data")
    @Operation(summary = "同步所有数据")
    public CommonResult<List<LinbanUser>> syncUserList() throws JsonProcessingException {

        linBanUserService.syncAllData();
        return null;
    }

    @PermitAll
    @GetMapping("/sync-all-linban")
    @Operation(summary = "同步所有的林班信息")
    public CommonResult<List<LinbanDO>> syncAllLinban() throws JsonProcessingException {
        List<LinbanDO> res = linBanUserService.syncAllLinban();
        return CommonResult.success(res);
    }

    @PermitAll
    @GetMapping("/sync-all-linban-user-relationship")
    @Operation(summary = "同步所有的用户林班关联信息")
    public CommonResult<List<LinbanUser>> syncAllLinbanUserRelationship() throws JsonProcessingException {
        List<LinbanUser> res = linBanUserService.syncAllLinbanUserRelation();
        return CommonResult.success(res);
    }


    @PermitAll()
    @PostMapping("/login-im")
    @Operation(summary = "登录im系统和林班系统")
    public CommonResult<Object> loginImAndLinban(@RequestBody LoginAccountReq req) throws JsonProcessingException {
        LoginRes res = linBanUserService.loginImAndLinban(req);
        return CommonResult.success(res);
    }

    @PermitAll
    @GetMapping("/get-all-linban")
    @Operation(summary = "获取用户的所有的林班列表")
    public CommonResult<List<LinbanDO>> getAllLinban(@RequestParam("userId") Long userId){
        List<LinbanDO> res = linBanUserService.getAllLinban(userId);
        return CommonResult.success(res);
    }

    @PermitAll
    @GetMapping("/get-same-linban-user-list")
    @Operation(summary = "获取同一个林班的所有用户列表")
    public CommonResult<List<LinbanUser>> getSameLinbanUserList(@RequestParam("linbanId") Long linbanId){
        List<LinbanUser> res = linBanUserService.getSameLinbanUserList(linbanId);
        return CommonResult.success(res);
    }


    @PermitAll
    @GetMapping("/get-all-friends")
    @Operation(summary = "获取同一个林班的所有用户列表")
    public CommonResult<List<Friend>> getAllFriends(@RequestParam("imUserId") Long imUserId){
        List<Friend> res = linBanUserService.getAllFriends(imUserId);
        return CommonResult.success(res);
    }

    @PermitAll
    @GetMapping("/get-all-linban-by-api")
    @Operation(summary = "获取当前的用户的林班列表")
    public CommonResult<List<LinbanRes>> getAllLinbanByApi(@RequestParam("employeesId") Long employeesId){
        List<LinbanRes> res = linBanUserService.getAllLinbanByApi(employeesId);
        return CommonResult.success(res);
    }

    @Resource
    private LabelService labelService;

    @PermitAll
    @GetMapping("/get-all-label-by-linbanId")
    @Operation(summary = "获取林班的所有标注")
    public CommonResult<List<LabelDO>> getAllLabelByLinbanId(@RequestParam(value = "quartelId",required = false) Long quartelId ,
                                                             @RequestParam(value = "userId",required = false)Long userId){
        List<LabelDO> res = labelService.getAllLabelByLinbanId(quartelId,userId);
        return CommonResult.success(res);
    }

    @PermitAll
    @GetMapping("/get-all-in-linban")
    @Operation(summary = "获取林班下所有的信息")
    public CommonResult<LinbanUserRelation> linbanRelation(@RequestParam("id") Long id , @RequestParam("quartelName")String quartelName) {
        LinbanUserRelation res = linBanUserService.linbanRelation(id, quartelName);
        if(CollectionUtil.isNotEmpty(res.getEmployees_list())){
            Set<Long> userIds = res.getEmployees_list().stream().map(UserVo::getId).collect(Collectors.toSet());
            List<LinbanUser> linbanUsers = linBanUserService.listByIds(userIds);
            res.setUsers(linbanUsers);
        }
        return CommonResult.success(res);
    }

}
