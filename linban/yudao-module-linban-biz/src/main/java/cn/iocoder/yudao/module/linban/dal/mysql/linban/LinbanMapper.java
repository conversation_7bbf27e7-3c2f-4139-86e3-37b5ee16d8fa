package cn.iocoder.yudao.module.linban.dal.mysql.linban;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.linban.dal.dataobject.linban.LinbanDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.linban.controller.admin.linban.vo.*;

/**
 * 林班 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LinbanMapper extends BaseMapperX<LinbanDO> {

    default PageResult<LinbanDO> selectPage(LinbanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LinbanDO>()
                .eqIfPresent(LinbanDO::getNo, reqVO.getNo())
                .eqIfPresent(LinbanDO::getLinbanArea, reqVO.getLinbanArea())
                .eqIfPresent(LinbanDO::getDominantTreeSpecies, reqVO.getDominantTreeSpecies())
                .eqIfPresent(LinbanDO::getTreeAge, reqVO.getTreeAge())
                .eqIfPresent(LinbanDO::getCanopyDensity, reqVO.getCanopyDensity())
                .eqIfPresent(LinbanDO::getAddress, reqVO.getAddress())
                .eqIfPresent(LinbanDO::getLatitude, reqVO.getLatitude())
                .eqIfPresent(LinbanDO::getLongitude, reqVO.getLongitude())
                .betweenIfPresent(LinbanDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(LinbanDO::getUploadImage, reqVO.getUploadImage())
                .betweenIfPresent(LinbanDO::getUploadTime, reqVO.getUploadTime())
                .eqIfPresent(LinbanDO::getTagsId, reqVO.getTagsId())
                .eqIfPresent(LinbanDO::getIsRelated, reqVO.getIsRelated())
                .orderByDesc(LinbanDO::getId));
    }

}