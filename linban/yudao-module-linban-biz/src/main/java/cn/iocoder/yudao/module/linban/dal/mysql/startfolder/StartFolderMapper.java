package cn.iocoder.yudao.module.linban.dal.mysql.startfolder;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.linban.dal.dataobject.startfolder.StartFolderDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.linban.controller.admin.startfolder.vo.*;

/**
 * 林班-收藏夹 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StartFolderMapper extends BaseMapperX<StartFolderDO> {

    default PageResult<StartFolderDO> selectPage(StartFolderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<StartFolderDO>()
                .eqIfPresent(StartFolderDO::getUserId, reqVO.getUserId())
                .eqIfPresent(StartFolderDO::getType, reqVO.getType())
                .likeIfPresent(StartFolderDO::getFolderName, reqVO.getFolderName())
                .eqIfPresent(StartFolderDO::getCount, reqVO.getCount())
                .betweenIfPresent(StartFolderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(StartFolderDO::getId));
    }

}