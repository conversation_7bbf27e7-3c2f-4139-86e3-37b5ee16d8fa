package cn.iocoder.yudao.module.linban.service.aerial;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.iocoder.yudao.module.linban.controller.admin.aerial.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.aerial.AerialDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 林班-航拍数据集 Service 接口
 *
 * <AUTHOR>
 */
public interface AerialService {

    /**
     * 创建林班-航拍数据集
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAerial(@Valid AerialSaveReqVO createReqVO);

    /**
     * 更新林班-航拍数据集
     *
     * @param updateReqVO 更新信息
     */
    void updateAerial(@Valid AerialSaveReqVO updateReqVO);

    /**
     * 删除林班-航拍数据集
     *
     * @param id 编号
     */
    void deleteAerial(Long id);

    /**
     * 获得林班-航拍数据集
     *
     * @param id 编号
     * @return 林班-航拍数据集
     */
    AerialDO getAerial(Long id);

    /**
     * 获得林班-航拍数据集分页
     *
     * @param pageReqVO 分页查询
     * @return 林班-航拍数据集分页
     */
    PageResult<AerialDO> getAerialPage(AerialPageReqVO pageReqVO);

}