package cn.iocoder.yudao.module.linban.service.aerial;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.module.linban.controller.admin.aerial.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.aerial.AerialDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.linban.dal.mysql.aerial.AerialMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AERIAL_NOT_EXISTS;


/**
 * 林班-航拍数据集 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AerialServiceImpl implements AerialService {

    @Resource
    private AerialMapper aerialMapper;

    @Override
    public Long createAerial(AerialSaveReqVO createReqVO) {
        // 插入
        AerialDO aerial = BeanUtils.toBean(createReqVO, AerialDO.class);
        aerialMapper.insert(aerial);
        // 返回
        return aerial.getId();
    }

    @Override
    public void updateAerial(AerialSaveReqVO updateReqVO) {
        // 校验存在
        validateAerialExists(updateReqVO.getId());
        // 更新
        AerialDO updateObj = BeanUtils.toBean(updateReqVO, AerialDO.class);
        aerialMapper.updateById(updateObj);
    }

    @Override
    public void deleteAerial(Long id) {
        // 校验存在
        validateAerialExists(id);
        // 删除
        aerialMapper.deleteById(id);
    }

    private void validateAerialExists(Long id) {
        if (aerialMapper.selectById(id) == null) {
            throw exception(AERIAL_NOT_EXISTS);
        }
    }

    @Override
    public AerialDO getAerial(Long id) {
        return aerialMapper.selectById(id);
    }

    @Override
    public PageResult<AerialDO> getAerialPage(AerialPageReqVO pageReqVO) {
        return aerialMapper.selectPage(pageReqVO);
    }

}