package cn.iocoder.yudao.module.linban.service.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.http.HttpUtils;
import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.req.UpdateUserAliveReq;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.res.AliveByJobRes;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.res.LinbanRes;
import cn.iocoder.yudao.module.linban.controller.app.user.vo.res.LoginRes;
import cn.iocoder.yudao.module.linban.controller.app.vo.ResponseVO;
import cn.iocoder.yudao.module.linban.controller.app.vo.linban.LibanListReq;
import cn.iocoder.yudao.module.linban.controller.app.vo.linban.LinbanUserRelation;
import cn.iocoder.yudao.module.linban.controller.app.vo.linban.PageVO;
import cn.iocoder.yudao.module.linban.controller.app.vo.user.LoginAccountReq;
import cn.iocoder.yudao.module.linban.controller.app.vo.user.RegisteAccountResponse;
import cn.iocoder.yudao.module.linban.controller.app.vo.user.RegisteAccountVo;
import cn.iocoder.yudao.module.linban.controller.app.vo.user.UserVo;
import cn.iocoder.yudao.module.linban.dal.dataobject.linban.LinbanDO;
import cn.iocoder.yudao.module.linban.dal.dataobject.linban.LinbanUserDO;
import cn.iocoder.yudao.module.linban.dal.dataobject.user.LinbanUser;
import cn.iocoder.yudao.module.linban.dal.dataobject.usertrack.UserTrackDO;
import cn.iocoder.yudao.module.linban.dal.mongo.friend.Friend;
import cn.iocoder.yudao.module.linban.dal.mongo.friend.FriendService;
import cn.iocoder.yudao.module.linban.dal.mysql.user.LinBanUserMapper;
import cn.iocoder.yudao.module.linban.service.linban.LinbanService;
import cn.iocoder.yudao.module.linban.service.linban.LinbanUserService;
import cn.iocoder.yudao.module.linban.service.usertrack.UserTrackService;
import cn.iocoder.yudao.module.linban.utils.TimeUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LinBanUserServiceImpl extends ServiceImpl<LinBanUserMapper, LinbanUser> implements LinBanUserService {

    @Resource
    private UserTrackService userTrackService;

    @Resource
    private FriendService friendService;

    private static String userListApi = "https://www.senfalinye.com/pounds/Gis.Index/getUserInfo";

    private static String linbanListApi = "https://www.senfalinye.com/pounds/Gis.Index/getQuartelList?limit=999999";

    private static String linbanAllUserApi = "https://www.senfalinye.com/pounds/Gis.Index/getQuartelInfo";

    private static String UserLinbanApi = "https://www.senfalinye.com/pounds/Gis.Index/getEmployeesQuartel";


    @Value("${im.api}")
    private String imHost;

    @Value("${im.loginApi}")
    private String ImLoginApi;

    @Value("${im.registeApi}")
    private String imRegisteApi;

    @Resource
    private LinbanService linbanService;


    @Override
    public List<LinbanUser> listByUser(LinbanUser req) {

        LambdaQueryWrapperX<LinbanUser> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(LinbanUser::getJobId,req.getJobId());
        wrapper.eqIfPresent(LinbanUser::getJobGroupId,req.getJobGroupId());

        List<LinbanUser> res = this.list(wrapper);
        return res;
    }


    @Override
    public AliveByJobRes aliveByJob() {
        AliveByJobRes res = new AliveByJobRes();
        List<LinbanUser> list = this.list();
        int aliveSize = list.stream().filter(ite -> ite.getIsAlive() == 1).collect(Collectors.toList()).size();
        res.setAllUser(list.size());
        res.setAliveUser(aliveSize);

        Map<String, AliveByJobRes> aliveMap = new HashMap<>();
        Map<String, List<LinbanUser>> jobGroup = list.stream().collect(Collectors.groupingBy(LinbanUser::getJob));
        for (String ite : jobGroup.keySet()) {
            List<LinbanUser> jobItem = jobGroup.getOrDefault(ite, new LinkedList<>());
            int aliveUserSize = jobItem.stream().filter(user -> user.getIsAlive() == 1).collect(Collectors.toList()).size();
            AliveByJobRes map = new AliveByJobRes();
            map.setAllUser(jobItem.size());
            map.setAliveUser(aliveUserSize);

            aliveMap.put(ite,map);
        }

        res.setJobGroup(aliveMap);
        return res;
    }

    public AliveByJobRes aliveByJobGroup() {
        AliveByJobRes res = new AliveByJobRes();
        List<LinbanUser> list = this.list();
        int aliveSize = list.stream().filter(ite -> ite.getIsAlive() == 1).collect(Collectors.toList()).size();
        res.setAllUser(list.size());
        res.setAliveUser(aliveSize);

        Map<String, AliveByJobRes> aliveMap = new HashMap<>();
        Map<String, List<LinbanUser>> jobGroup = list.stream().collect(Collectors.groupingBy(LinbanUser::getJobGroup));
        for (String ite : jobGroup.keySet()) {
            List<LinbanUser> jobItem = jobGroup.getOrDefault(ite, new LinkedList<>());
            int aliveUserSize = jobItem.stream().filter(user -> user.getIsAlive() != null && user.getIsAlive().equals(1)).collect(Collectors.toList()).size();
            AliveByJobRes map = new AliveByJobRes();
            map.setAllUser(jobItem.size());
            map.setAliveUser(aliveUserSize);

            aliveMap.put(ite,map);
        }

        res.setJobGroup(aliveMap);
        return res;
    }

    @Override
    public LinbanUser updateUserAlive(UpdateUserAliveReq req) {

        LinbanUser user = this.getById(req.getUserId());
        user.setIsAlive(1);
        user.setLastAliveTime(LocalDateTime.now());
        user.setLatitude(req.getLatitude());
        user.setLongitude(req.getLongitude());

        boolean b = this.updateById(user);
        UserTrackDO userTrackDO = new UserTrackDO();
        userTrackDO.setUserId(user.getId());
        userTrackDO.setLatitude(req.getLatitude());
        userTrackDO.setLongitude(req.getLongitude());
        userTrackDO.setJobId(user.getJobId());
        userTrackDO.setJobGroupId(user.getJobGroupId());
        userTrackDO.setCreateTime(TimeUtils.getNowString());

        userTrackService.save(userTrackDO);
        return user;
    }



//    @Transactional
    public void syncAllData() throws JsonProcessingException {

        // 同步获取林班列表
        List<LinbanDO> linbanList = syncLinbanData();
        List<Long> ids = linbanList.stream().map(LinbanDO::getId).collect(Collectors.toList());
        List<LinbanUser> linbanUsers = syncAllLinbanUserRelation(ids);
        if(CollectionUtil.isEmpty(linbanList)) {
            linbanList = linbanService.list();
        }
        // 同步获取用户列表
        List<LinbanUser> linbanUser = syncUserList();
        if(CollectionUtil.isEmpty(linbanUser)) {
            linbanUser = this.list();
        }
        Map<Long, String> userImUserAccount = linbanUser.stream().collect(Collectors.toMap(LinbanUser::getId, ite -> ite.getImUserId() == null?"-1":ite.getImUserId()));
        // 同步所有的好友关系
        Map<Long,Set<Long>> relationship = getRelationShip(linbanList);

//        this.saveBatch(linbanUser);
        syncAllFriends(relationship,userImUserAccount);
    }



    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void syncAllFriends(Map<Long,Set<Long>> relationship,Map<Long, String> userImUserAccount) throws JsonProcessingException {
        log.info("**************************\n");
        log.info(" 好友列表 {}" , relationship.keySet().size());
        log.info("\n**************************");
        for (Long userId : relationship.keySet()) {
            String ownerId = userImUserAccount.get(userId);
            if(StrUtil.isNotEmpty(ownerId)){
                List<String> friendsIds = relationship.get(userId).stream().map(ite -> {
                    String friends = userImUserAccount.get(ite);
                    return friends;
                }).filter(StrUtil::isNotEmpty).collect(Collectors.toList());

                friendService.syncFriends(ownerId,friendsIds);
            }
        }
    }


    private Map<Long, Set<Long>> getRelationShip(List<LinbanDO> linbanList) throws JsonProcessingException {
        Map<Long,Set<Long>> res = new HashMap<>();
        for (LinbanDO ite : linbanList) {
            //将所有关系
            getAllUserInOneLinban(ite,res);
        }
        // 现在用户已经有了所有的关联关系了
        return res;
    }

    @Resource
    private LinbanUserService linbanUserService;

    public void getAllUserInOneLinban(LinbanDO ite , Map<Long,Set<Long>> res) throws JsonProcessingException {
        Map<String, String> header = new HashMap<>();

        Map<String, Object> data = new HashMap<>();
        data.put("quartel_id",ite.getId());
        data.put("quartel_name",ite.getQuartelName());

        String post = HttpUtils.postForm(linbanAllUserApi, header, data);
        ResponseVO<LinbanUserRelation> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<LinbanUserRelation>>() {});

        List<LinbanUserDO> linbanUserDOList = new LinkedList<>();
        LambdaQueryWrapperX<LinbanUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(LinbanUserDO::getLinbanId,ite.getId());
        List<LinbanUserDO> existedLinbanUser = linbanUserService.list(wrapper);
        Set<Long> existedUserId = existedLinbanUser.stream().map(LinbanUserDO::getUserId).collect(Collectors.toSet());

        log.info("***************************");
        log.info("{}\n",response.getData().getEmployees_list());
        log.info("***************************");

        // 获取到一个林班下所有的用户关联关系
        if(response.getData() != null){
            if(response.getData().getEmployees_list() != null){
                for (UserVo userVo : response.getData().getEmployees_list()) {
//            if(existedUserId.contains(userVo.getId())) {
//                continue;
//            }
                    Long id = userVo.getId();
                    Set<Long> currentUserFriend = res.getOrDefault(id, new HashSet<>());
                    Set<Long> newCurrentUserFriend = response.getData().getEmployees_list().stream()
                            .filter(user -> !user.getId().equals(userVo.getId()))
                            .map(UserVo::getId).collect(Collectors.toSet());

                    // 更新所有的关系
                    currentUserFriend.addAll(newCurrentUserFriend);
                    res.put(id,currentUserFriend);
                    // 数据库保存对应的关系
                    LinbanUserDO linbanUserDO = new LinbanUserDO();
                    linbanUserDO.setUserId(userVo.getId());
                    linbanUserDO.setLinbanId(ite.getId());

                    linbanUserDOList.add(linbanUserDO);
                }
//        linbanUserService.saveBatch(linbanUserDOList);
            }
        }

    }


    public LinbanUserRelation linbanRelation(Long id , String quartelName) {
        Map<String, String> header = new HashMap<>();

        Map<String, Object> data = new HashMap<>();
        data.put("quartel_id", id);
        data.put("quartel_name", quartelName);

        String post = HttpUtils.postForm(linbanAllUserApi, header, data);
        ResponseVO<LinbanUserRelation> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<LinbanUserRelation>>() {});

        LinbanUserRelation res = response.getData();
        return res;
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<LinbanDO> syncLinbanData() {
        List<LibanListReq> linbanList = getAllLinban();
        Set<Long> linbanIds = linbanList.stream().map(LibanListReq::getId).collect(Collectors.toSet());
        List<LinbanDO> existLinban = linbanService.listByIds(linbanIds);
        Set<Long> existedLinbanIds = existLinban.stream().map(LinbanDO::getId).collect(Collectors.toSet());

        List<LinbanDO> res = new LinkedList<>();
        for (LibanListReq ite : linbanList) {
            // 如果存在的话则跳过
            if(existedLinbanIds.contains(ite.getId())) {
                continue;
            }

            LinbanDO linbanDO = new LinbanDO(ite);
            res.add(linbanDO);
        }
        linbanService.saveBatch(res);
        return res;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public  List<LinbanUser> syncUserList() throws JsonProcessingException {

        List<UserVo> userFromApi = getAllUserAccount();
        Set<Long> userVoIds = userFromApi.stream().map(UserVo::getId).collect(Collectors.toSet());
        LambdaQueryWrapperX<LinbanUser> wrapper = new LambdaQueryWrapperX<>();
        wrapper.inIfPresent(LinbanUser::getId, userVoIds);
        List<LinbanUser> existedUser = this.list(wrapper);
        Set<Long> existedUserIdSet = existedUser.stream().map(LinbanUser::getId).collect(Collectors.toSet());

        List<LinbanUser> res = new LinkedList<>();
        for (UserVo ite : userFromApi) {
            ite.setPhone(ite.getPhone().replaceAll(" ", ""));
            log.info("{}",ite);
            // 如果当前用户已经存在则跳过
            if(existedUserIdSet.contains(ite.getId())) {
               continue;
            }

            // 创建林班用户并创建im账号
            // 同步注册im账号
            RegisteAccountResponse registeAccountResponse = registeImAccount(ite);
            log.info("{}",registeAccountResponse);
            LinbanUser linbanUser = new LinbanUser();
            linbanUser.setUserName(ite.getName());
            linbanUser.setAccount(ite.getPhone());
            linbanUser.setUserName(ite.getName());
            linbanUser.setId(ite.getId());
            linbanUser.setPhoneNumber(ite.getPhone());
            linbanUser.setUserStatus(ite.getStatus());
            linbanUser.setGrade(ite.getGrade());
            if(registeAccountResponse != null) {
                linbanUser.setImUserId(registeAccountResponse.getUserID());
            }
//            this.save(linbanUser);
            res.add(linbanUser);
        }
        this.saveBatch(res);
        return res;
    }

    public List<LibanListReq> getAllLinban() {
        String post = HttpUtils.post(linbanListApi, null, null);
        ResponseVO<PageVO<LibanListReq>> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<PageVO<LibanListReq>>>() {});

        return response.getData().getList();
    }

    public List<LinbanDO> syncAllLinban() {
        List<LinbanDO> allData = linbanService.list();
        Set<Long> allDataIds = allData.stream().map(LinbanDO::getId).collect(Collectors.toSet());
        Map<Long, LinbanDO> allDataMap = allData.stream().collect(Collectors.toMap(LinbanDO::getId, ite -> ite));

        List<LibanListReq> apiAllLinban = getAllLinban();
        List<Long> apiAllLban = apiAllLinban.stream().map(LibanListReq::getId).collect(Collectors.toList());
        List<List<Long>> collectIds = CollectionUtils.diffList(allDataIds, apiAllLban, Long::equals);

        List<Long> longs = collectIds.get(0);
        List<Long> removeIds = collectIds.get(2);
        Set<Long> removeSetIds = new HashSet<>(removeIds);
        LinkedList<LinbanDO> adds = new LinkedList<>();
        LinkedList<LinbanDO> updates = new LinkedList<>();
        LinkedList<LinbanDO> removes = new LinkedList<>();

        for (LibanListReq ite : apiAllLinban) {
            if(!allDataIds.contains(ite.getId())) {
                adds.add(new LinbanDO(ite));
            } else {
                if(removeSetIds.contains(ite.getId())) {
                    removes.add(new LinbanDO(ite));
                } else {
                    LinbanDO origin = allDataMap.getOrDefault(ite.getId(), new LinbanDO());
                    if(!origin.equals(ite)) {
                        updates.add(new LinbanDO(ite));
                    }
                }
            }
        }


        linbanService.saveBatch(adds);
        linbanService.updateBatchById(updates);
        linbanService.removeBatchByIds(removes);

        return linbanService.list();
    }







    private List<UserVo> getAllUserAccount() {
        String post = HttpUtils.post(userListApi, null, null);
        ResponseVO<List<UserVo>> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<List<UserVo>>>() {});
        log.info("{} \n {}",response,response.getData());
        return response.getData();
    }


    private RegisteAccountResponse registeImAccount(UserVo user) throws JsonProcessingException {
        Map<String, String> header = new HashMap<>();
        header.put("operationid", UUID.randomUUID().toString());

        RegisteAccountVo registeAccountVo = new RegisteAccountVo(user.getName(), user.getPhone());
        Map<String, Object> data = new HashMap<>();
        data.put("verifyCode","666666");
        data.put("autoLogin",true);
        data.put("user",registeAccountVo);
        data.put("platform",5);


        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String requestBody = mapper.writeValueAsString(data);
        String post = HttpUtils.post(imRegisteApi, header, requestBody);
        ResponseVO<RegisteAccountResponse> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<RegisteAccountResponse>>() {});
        if(response.getErrCode().equals(20004)){
            // 重复注册
            LoginAccountReq req = new LoginAccountReq(user.getPhone());
            requestBody = mapper.writeValueAsString(req);
            post = HttpUtils.post(ImLoginApi, header, requestBody);
            response = JSONObject.parseObject(post, new TypeReference<ResponseVO<RegisteAccountResponse>>() {});
        }
        return response.getData();
    }





    @Override
    public LoginRes loginImAndLinban(LoginAccountReq req) throws JsonProcessingException {

        LoginRes res = new LoginRes();
        Map<String, String> header = new HashMap<>();
        header.put("operationid", UUID.randomUUID().toString());

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String requestBody = mapper.writeValueAsString(req);
        String post = HttpUtils.post(ImLoginApi, header, requestBody);
        ResponseVO<RegisteAccountResponse> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<RegisteAccountResponse>>() {});

        if(response != null){
            RegisteAccountResponse data = response.getData();
            if(data != null){
                res.setChatToken(data.getChatToken());
                res.setUserID(data.getUserID());
                res.setImToken(data.getImToken());
            }
        }

        res.setData(response);
        LambdaQueryWrapperX<LinbanUser> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(LinbanUser::getPhoneNumber,req.getPhoneNumber());

        LinbanUser one = this.getOne(wrapper, false);
        res.setLinBanUser(one);

        return res;
    }

    @Resource
    private LinbanUserService linBanUserService;;

    @Override
    public List<LinbanDO> getAllLinban(Long userId) {

        LambdaQueryWrapperX<LinbanUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(LinbanUserDO::getUserId,userId);
        List<LinbanUserDO> list = linBanUserService.list(wrapper);
        if(CollectionUtil.isEmpty(list)){
            return Collections.emptyList();
        }

        Set<Long> linbanIds = list.stream().map(LinbanUserDO::getLinbanId).collect(Collectors.toSet());
        List<LinbanDO> res = linbanService.listByIds(linbanIds);
        return res;
    }

    @Override
    public List<LinbanUser> getSameLinbanUserList(Long linbanId) {
        LambdaQueryWrapperX<LinbanUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(LinbanUserDO::getLinbanId,linbanId);
        List<LinbanUserDO> list = linBanUserService.list(wrapper);
        if(CollectionUtil.isEmpty(list)){
            return Collections.emptyList();
        }
        Set<Long> userIds = list.stream().map(LinbanUserDO::getUserId).collect(Collectors.toSet());
        List<LinbanUser> res = this.listByIds(userIds);

        return res;
    }

    @Override
    public List<Friend> getAllFriends(Long imUserId) {
        return Collections.emptyList();
    }



    @Override
    public List<LinbanRes> getAllLinbanByApi(Long employeesId) {

        Map<String, Object> data = new HashMap<>();
        data.put("employees_id", employeesId);
        String post = HttpUtils.postForm(UserLinbanApi, null, data);
        System.out.println(post);
        ResponseVO<List<LinbanRes>> response = JSONObject.parseObject(post, new TypeReference<ResponseVO<List<LinbanRes>>>() {});
        return response.getData();
    }


    public List<LinbanUserDO> syncOneLinbanUserRelation(Long employeesId) {

        List<LinbanRes> allLinbanByApi = getAllLinbanByApi(employeesId);
        List<Long> linbanIds = allLinbanByApi.stream().map(LinbanRes::getId).collect(Collectors.toList());

        LambdaQueryWrapperX<LinbanUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(LinbanUserDO::getUserId,employeesId);
        wrapper.inIfPresent(LinbanUserDO::getLinbanId,linbanIds);
        List<LinbanUserDO> allData = linBanUserService.list(wrapper);
        List<Long> existedLinbanIds = allData.stream().map(LinbanUserDO::getLinbanId).collect(Collectors.toList());
        Map<Long, LinbanUserDO> allDataMap = allData.stream().collect(Collectors.toMap(LinbanUserDO::getLinbanId, ite -> ite));

        List<List<Long>> lists = CollectionUtils.diffList(existedLinbanIds, linbanIds, Long::equals);

        List<Long> create = lists.get(0);
        List<Long> update = lists.get(1);
        List<Long> remove = lists.get(2);

        LinkedList<LinbanUserDO> created = new LinkedList<>();
        LinkedList<LinbanUserDO> removed = new LinkedList<>();
        for (Long ite : create) {
            LinbanUserDO linbanUser = new LinbanUserDO(employeesId,ite);
            created.add(linbanUser);
        }

        for (Long ite : remove) {
            LinbanUserDO data = allDataMap.getOrDefault(ite, new LinbanUserDO());
            removed.add(data);
        }

        linbanUserService.saveBatch(created);
        linbanUserService.removeBatchByIds(removed);


        return created;
    }

    public List<LinbanUser> syncAllLinbanUserRelation() {
        List<UserVo> userFromApi = getAllUserAccount();

        for (UserVo ite : userFromApi) {
            List<LinbanUserDO> linbanUserDOList = syncOneLinbanUserRelation(ite.getId());
            log.info("*******************");
            log.info(" size = {} \n {}", linbanUserDOList.size());
            log.info(" {}", linbanUserDOList);
            log.info("*******************");
        }
        return null;
    }

    public List<LinbanUser> syncAllLinbanUserRelation(List<Long> ids) {

        for (Long ite : ids) {
            List<LinbanUserDO> linbanUserDOList = syncOneLinbanUserRelation(ite);
            log.info("*******************");
            log.info(" size = {} \n {}", linbanUserDOList.size());
            log.info(" {}", linbanUserDOList);
            log.info("*******************");
        }
        return null;
    }


}

