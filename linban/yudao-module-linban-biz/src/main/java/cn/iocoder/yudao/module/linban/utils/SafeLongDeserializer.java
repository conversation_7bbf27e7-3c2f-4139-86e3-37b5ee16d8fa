package cn.iocoder.yudao.module.linban.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import cn.hutool.core.util.StrUtil;

import java.io.IOException;

/**
 * 安全的Long类型反序列化器
 * 用于处理前端传递的"undefined"、"null"等无效值
 * 
 * <AUTHOR>
 */
public class SafeLongDeserializer extends JsonDeserializer<Long> {

    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        String value = p.getValueAsString();
        
        // 处理空值或无效值
        if (StrUtil.isBlank(value) || 
            "undefined".equalsIgnoreCase(value) || 
            "null".equalsIgnoreCase(value)) {
            return null;
        }
        
        try {
            return Long.parseLong(value.trim());
        } catch (NumberFormatException e) {
            // 如果无法解析，返回null而不是抛出异常
            return null;
        }
    }
}
