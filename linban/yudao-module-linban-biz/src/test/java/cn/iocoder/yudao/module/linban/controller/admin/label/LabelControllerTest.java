package cn.iocoder.yudao.module.linban.controller.admin.label;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbAndRedisUnitTest;
import cn.iocoder.yudao.module.linban.controller.admin.label.vo.LabelPageReqVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LabelController 测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("unit-test")
public class LabelControllerTest extends BaseDbAndRedisUnitTest {

    @Test
    public void testLabelPageReqVODeserialization() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 测试正常的JSON
        String normalJson = "{\"userId\":\"123\",\"quartelId\":\"456\",\"folderId\":\"789\"}";
        LabelPageReqVO normalVO = objectMapper.readValue(normalJson, LabelPageReqVO.class);
        assertEquals(Long.valueOf(123), normalVO.getUserId());
        assertEquals(Long.valueOf(456), normalVO.getQuartelId());
        assertEquals(Long.valueOf(789), normalVO.getFolderId());
        
        // 测试包含"undefined"的JSON
        String undefinedJson = "{\"userId\":\"undefined\",\"quartelId\":\"456\",\"folderId\":\"null\"}";
        LabelPageReqVO undefinedVO = objectMapper.readValue(undefinedJson, LabelPageReqVO.class);
        assertNull(undefinedVO.getUserId());
        assertEquals(Long.valueOf(456), undefinedVO.getQuartelId());
        assertNull(undefinedVO.getFolderId());
        
        // 测试空值
        String emptyJson = "{\"userId\":\"\",\"quartelId\":\"456\",\"folderId\":null}";
        LabelPageReqVO emptyVO = objectMapper.readValue(emptyJson, LabelPageReqVO.class);
        assertNull(emptyVO.getUserId());
        assertEquals(Long.valueOf(456), emptyVO.getQuartelId());
        assertNull(emptyVO.getFolderId());
        
        // 测试无效数字
        String invalidJson = "{\"userId\":\"abc\",\"quartelId\":\"456\",\"folderId\":\"xyz\"}";
        LabelPageReqVO invalidVO = objectMapper.readValue(invalidJson, LabelPageReqVO.class);
        assertNull(invalidVO.getUserId());
        assertEquals(Long.valueOf(456), invalidVO.getQuartelId());
        assertNull(invalidVO.getFolderId());
    }
}
